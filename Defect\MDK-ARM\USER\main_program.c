#include "main_program.h"
#include <stdio.h>
#include "time_handle.h"
#include "oled.h"
#include "dht11.h"
#include "esp8266.h"
#include "led.h"
#include "atgm336h.h"
#include "v831.h"
#include "motor.h"
#include "servo.h"
#include "delay.h"
#include "vl53l0x_wmd_api.h"
/*
*************************************
�궨��
*************************************
*/

/*
*************************************
��������
*************************************
*/
extern uint16_t index_oled;
extern uint16_t index_dht11;
extern uint16_t index_gps; 
extern uint8_t index_send_msg;
extern uint8_t index_led;
extern uint16_t index_v831;
extern uint16_t index_vl53l0x;
extern uint16_t index_encoder;

/*
*************************************
��������
*************************************
*/

/*
*************************************
��������
*************************************
*/
uint8_t led_status =0;
uint8_t temp_value =0;
uint8_t humi_value =0;
uint16_t Distance = 0;
float travel_distance = 0.0f;

/*
*************************************
��������
*************************************
*/

/**
  * @brief          ��ʼ������,�൱��Arduino��setup()����,ֻ�ڳ�ʼ��ʱ��ִ��һ��
  * @param[in]      none
  * @retval         none
  */
void user_init_program(void)
{
	delay_init();
  OLED_init(); 
  //esp8266_init();
  atgm336h_init();
  v831_init(); // ��ʼ��V831
	Motor_Init();
	SERVO_Init(&htim4);
	Encoder_Init();
	VL53L0X_Init();
  while(dht11_init())
  {
     HAL_Delay(500);
    printf("dht11 init faild\r\n");
    OLED_printf (0,0,"dht11 init faild");
  }
  OLED_printf (0,0,"  Alibaba Cloud IOT ");
	HAL_Delay(500);
  OLED_operate_gram(PEN_CLEAR);
	time_slot_start();
	HAL_Delay(100);
	SERVO_Release();
	// �������
  Motor_Control(MOTOR_FORWARD);
}

/**
  * @brief          ��ʼ������,�൱��Arduino��loop()����,һֱִ�иú���
  * @param[in]      none
  * @retval         none
  */
void user_main_program(void)
{
  if(esp8266_receive_msg()!=1)
  {
  }
  if(index_dht11 ==1)
  {
    dht11_read_data(&temp_value,&humi_value);
		
    index_dht11=0;
  }
  if(index_led==1)
  {
    set_led(led_status);
    index_led=0;
  }
  if (index_gps == 1) 
  {
    parseGpsBuffer();  
    printGpsBuffer();  

    index_gps = 0;
  }
  if(index_v831 == 1) 
  {	
		parseV831Buffer();
    printV831Data();
    index_v831 = 0;
  }
	if(index_vl53l0x == 1)
  {
		Distance = VL53L0X_GetValue();
		index_vl53l0x = 0;
		if(Distance <= 60)
		{
			printf("VL53L0X Distance: %d mm\r\n", Distance);
			SERVO_Clamp();  
			Motor_Control(MOTOR_STOP);
		}
  }
	if(index_encoder == 1)
  {
        Encoder_Update();
        travel_distance = Encoder_GetDistanceMM();
        index_encoder = 0;
  }
  if(index_oled==1)
  {	
		
    OLED_printf (0,0,"temp:%d",temp_value);
    OLED_printf (0,10,"humi:%d",humi_value);
		
    OLED_printf (2, 0, "Lat:%.1f%c", g_LatAndLongData.latitude ,g_LatAndLongData.N_S);
    OLED_printf (2, 10, "Lon:%.1f%c", g_LatAndLongData.longitude ,g_LatAndLongData.E_W);
    
		OLED_printf(3, 0, "Dist:%d", Distance);
    OLED_printf(3, 10, "Travel:%.1f", travel_distance);
    // ��ʾV831�����
    OLED_printf(4, 0, "Error:%s", g_V831DetectionData.results[0].label );
    
    index_oled=0;
  }
if(index_send_msg==1)
{
    index_send_msg =0;
    if(esp8266_send_msg()==0)
   {
    }
   else
   {
    printf("msg send error\r\n");
  }
 }
}
